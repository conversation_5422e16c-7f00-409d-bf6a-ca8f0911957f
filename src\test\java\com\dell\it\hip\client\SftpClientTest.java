package com.dell.it.hip.client;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit test class for SftpClient that validates all SFTP operations
 * including file upload (handler testing), file download (adapter testing),
 * file processing, connection management, and error scenarios.
 * 
 * This test class follows the same patterns and structure as existing client tests
 * (MQClientTest, RabbitMQClientTest, KafkaClientTest) in the HIP services framework.
 */
class SftpClientTest {

    private static final Logger logger = LoggerFactory.getLogger(SftpClientTest.class);
    
    private SftpClientConfig consumerConfig;
    private SftpClientConfig producerConfig;
    private SftpClient sftpClient;
    
    // Test data
    private static final String TEST_FILE_PREFIX = "sftp-test-";
    private static final String TEST_CONTENT = "Test content from SftpClient - ";
    private static final String TEST_BINARY_CONTENT = "Binary test content with special chars: àáâãäåæçèéêë";
    
    @BeforeEach
    void setUp() {
        // Create configurations using the test properties
        consumerConfig = SftpClientConfig.createConsumerConfig();
        producerConfig = SftpClientConfig.createProducerConfig();
        
        // Create SftpClient instance
        sftpClient = new SftpClient(consumerConfig, producerConfig);
        
        logger.info("SftpClient test setup completed");
    }
    
    @AfterEach
    void tearDown() {
        if (sftpClient != null) {
            sftpClient.close();
        }
        logger.info("SftpClient test cleanup completed");
    }
    
    @Test
    void testSendMessage() throws Exception {
        // Test sending a simple text message (handler functionality)
        String fileName = TEST_FILE_PREFIX + "send-" + System.currentTimeMillis() + ".txt";
        String testMessage = TEST_CONTENT + System.currentTimeMillis();
        
        assertDoesNotThrow(() -> {
            sftpClient.sendMessage(fileName, testMessage);
        });
        
        logger.info("Successfully sent file: {} with content: {}", fileName, testMessage);
    }
    
    @Test
    void testSendByteArrayMessage() throws Exception {
        // Test sending a byte array message (handler functionality)
        String fileName = TEST_FILE_PREFIX + "binary-" + System.currentTimeMillis() + ".bin";
        byte[] testPayload = TEST_BINARY_CONTENT.getBytes("UTF-8");
        
        assertDoesNotThrow(() -> {
            sftpClient.sendMessage(fileName, testPayload);
        });
        
        logger.info("Successfully sent binary file: {} with {} bytes", fileName, testPayload.length);
    }
    
    @Test
    void testSendMessageWithHeaders() throws Exception {
        // Test sending a message with custom metadata (simulated via filename)
        String timestamp = String.valueOf(System.currentTimeMillis());
        String fileName = TEST_FILE_PREFIX + "headers-" + timestamp + ".xml";
        String testMessage = "<?xml version=\"1.0\"?><test><timestamp>" + timestamp + "</timestamp></test>";
        
        assertDoesNotThrow(() -> {
            sftpClient.sendMessage(fileName, testMessage);
        });
        
        logger.info("Successfully sent XML file: {}", fileName);
    }
    
    @Test
    void testReceiveMessage() throws Exception {
        // First send a message (adapter functionality)
        String fileName = TEST_FILE_PREFIX + "receive-" + System.currentTimeMillis() + ".txt";
        String testMessage = TEST_CONTENT + "for receive test";
        sftpClient.sendMessage(fileName, testMessage);
        
        // Wait a bit for the file to be available
        Thread.sleep(2000);
        
        // Then try to receive it
        SftpClient.SftpMessage receivedMessage = sftpClient.receiveMessage();
        
        assertNotNull(receivedMessage, "Should have received a message");
        assertNotNull(receivedMessage.getFileName(), "Received message should have a filename");
        assertNotNull(receivedMessage.getContent(), "Received message should have content");
        assertTrue(receivedMessage.getFileSize() > 0, "File size should be greater than 0");
        
        logger.info("Successfully received file: {} with content: {}", 
                   receivedMessage.getFileName(), receivedMessage.getContent());
    }
    
    @Test
    void testReceiveMessageTimeout() throws Exception {
        // Test receiving when no new files are available
        // Note: This might receive existing files, so we just verify it doesn't throw
        SftpClient.SftpMessage receivedMessage = sftpClient.receiveMessage();
        
        logger.info("Receive test completed, received: {}", 
                   receivedMessage != null ? receivedMessage.getFileName() : "null");
    }
    
    @Test
    void testFileFiltering() throws Exception {
        // Test file pattern filtering functionality
        String timestamp = String.valueOf(System.currentTimeMillis());
        
        // Send files with different extensions
        sftpClient.sendMessage(TEST_FILE_PREFIX + "filter-" + timestamp + ".txt", "Text file content");
        sftpClient.sendMessage(TEST_FILE_PREFIX + "filter-" + timestamp + ".xml", "<xml>XML content</xml>");
        sftpClient.sendMessage(TEST_FILE_PREFIX + "filter-" + timestamp + ".json", "{\"json\": \"content\"}");
        
        Thread.sleep(2000);
        
        // Test filtering for XML files only
        SftpClient.SftpMessage xmlMessage = sftpClient.receiveMessage("*.xml");
        
        if (xmlMessage != null) {
            assertTrue(xmlMessage.getFileName().endsWith(".xml"), 
                      "Filtered message should be an XML file");
            logger.info("Successfully filtered XML file: {}", xmlMessage.getFileName());
        } else {
            logger.info("No XML files found (may have been processed already)");
        }
    }
    
    @Test
    void testPostProcessing() throws Exception {
        // Test post-processing functionality (delete/rename)
        
        // Create a custom consumer config with delete post-processing
        SftpClientConfig deleteConfig = new SftpClientConfig.Builder()
            .host(consumerConfig.getHost())
            .port(consumerConfig.getPort())
            .username(consumerConfig.getUsername())
            .password(consumerConfig.getPassword())
            .remoteDirectory(consumerConfig.getRemoteDirectory())
            .postProcessAction("delete")
            .build();
        
        try (SftpClient deleteClient = new SftpClient(deleteConfig, producerConfig)) {
            String fileName = TEST_FILE_PREFIX + "delete-" + System.currentTimeMillis() + ".txt";
            String testMessage = TEST_CONTENT + "for delete test";
            
            // Send file
            deleteClient.sendMessage(fileName, testMessage);
            Thread.sleep(1000);
            
            // Receive file (should delete it)
            SftpClient.SftpMessage receivedMessage = deleteClient.receiveMessage();
            
            if (receivedMessage != null && receivedMessage.getFileName().equals(fileName)) {
                logger.info("Successfully processed and deleted file: {}", fileName);
            } else {
                logger.info("File may have been processed by another test or already deleted");
            }
        }
    }
    
    @Test
    void testCompressionHandling() throws Exception {
        // Test compression/decompression functionality
        
        // Create configs with compression enabled
        SftpClientConfig compressedConsumerConfig = new SftpClientConfig.Builder()
            .host(consumerConfig.getHost())
            .port(consumerConfig.getPort())
            .username(consumerConfig.getUsername())
            .password(consumerConfig.getPassword())
            .remoteDirectory(consumerConfig.getRemoteDirectory())
            .compressed(true)
            .build();
        
        SftpClientConfig compressedProducerConfig = new SftpClientConfig.Builder()
            .host(producerConfig.getHost())
            .port(producerConfig.getPort())
            .username(producerConfig.getUsername())
            .password(producerConfig.getPassword())
            .remoteDirectory(producerConfig.getRemoteDirectory())
            .gzipEnabled(true)
            .build();
        
        try (SftpClient compressedClient = new SftpClient(compressedConsumerConfig, compressedProducerConfig)) {
            String fileName = TEST_FILE_PREFIX + "compressed-" + System.currentTimeMillis() + ".txt";
            String testMessage = TEST_CONTENT + "for compression test - this is a longer message to test compression";
            
            assertDoesNotThrow(() -> {
                compressedClient.sendMessage(fileName, testMessage);
            });
            
            logger.info("Successfully tested compression with file: {}", fileName);
        }
    }
    
    @Test
    void testConnectionManagement() throws Exception {
        // Test connection lifecycle management
        
        // Test that client can handle multiple operations
        String fileName1 = TEST_FILE_PREFIX + "conn1-" + System.currentTimeMillis() + ".txt";
        String fileName2 = TEST_FILE_PREFIX + "conn2-" + System.currentTimeMillis() + ".txt";
        
        assertDoesNotThrow(() -> {
            sftpClient.sendMessage(fileName1, "First connection test");
            sftpClient.sendMessage(fileName2, "Second connection test");
        });
        
        // Test that connections are properly managed
        Thread.sleep(1000);
        
        assertDoesNotThrow(() -> {
            SftpClient.SftpMessage message = sftpClient.receiveMessage();
            logger.info("Connection management test - received: {}", 
                       message != null ? message.getFileName() : "null");
        });
        
        logger.info("Successfully tested connection management");
    }
    
    @Test
    void testMessageListener() throws Exception {
        AtomicInteger messageCount = new AtomicInteger(0);
        CountDownLatch latch = new CountDownLatch(2);
        
        // Start listener
        sftpClient.startListener(message -> {
            logger.info("Received file via listener: {}", message.getFileName());
            messageCount.incrementAndGet();
            latch.countDown();
        });
        
        // Send multiple files
        for (int i = 1; i <= 2; i++) {
            String fileName = TEST_FILE_PREFIX + "listener-" + i + "-" + System.currentTimeMillis() + ".txt";
            String testMessage = "Listener test file " + i;
            sftpClient.sendMessage(fileName, testMessage);
            Thread.sleep(500); // Small delay between files
        }
        
        // Wait for messages to be processed (with timeout)
        boolean received = latch.await(30, TimeUnit.SECONDS);
        sftpClient.stopListener();
        
        if (received) {
            assertTrue(messageCount.get() >= 1, "Should have processed at least 1 file");
            logger.info("Successfully processed {} files via listener", messageCount.get());
        } else {
            logger.info("Listener test completed with timeout - may have processed existing files");
        }
    }
    
    @Test
    void testWaitForMessages() throws Exception {
        AtomicInteger processedCount = new AtomicInteger(0);
        
        // Send files first
        for (int i = 1; i <= 2; i++) {
            String fileName = TEST_FILE_PREFIX + "wait-" + i + "-" + System.currentTimeMillis() + ".txt";
            String testMessage = "Wait test file " + i;
            sftpClient.sendMessage(fileName, testMessage);
        }
        
        // Wait for files to be processed
        try {
            List<SftpClient.SftpMessage> messages = sftpClient.waitForMessages(2, 30000, message -> {
                logger.info("Processed file: {}", message.getFileName());
                processedCount.incrementAndGet();
            });
            
            assertTrue(messages.size() >= 1, "Should have received at least 1 file");
            assertTrue(processedCount.get() >= 1, "Should have processed at least 1 file");
            
            logger.info("Successfully waited for and processed {} files", processedCount.get());
        } catch (RuntimeException e) {
            // This is expected if we don't receive the exact number within timeout
            logger.info("Wait for messages completed with timeout - processed {} files", processedCount.get());
        }
    }

    @Test
    void testCustomConfiguration() throws Exception {
        // Test with custom configuration
        SftpClientConfig customConsumerConfig = new SftpClientConfig.Builder()
            .host("DURNLDVAICBAT01.amer.dell.com")
            .port(22)
            .username("AMERICAS\\svc_npaicdvgsmrmqv1")
            .password("~UfIBrgxqj%2BVp%3FSTK69L178H")
            .remoteDirectory("/apps/aic/shared/test/custom")
            .fileNamePattern("custom-*.txt")
            .pollingIntervalMs(30000L)
            .compressed(false)
            .charset("UTF-8")
            .postProcessAction("rename")
            .renamePattern("{file}.processed")
            .build();

        SftpClientConfig customProducerConfig = new SftpClientConfig.Builder()
            .host("DURNLDVAICBAT01.amer.dell.com")
            .port(22)
            .username("svc_npaicbatch")
            .password("xK5RqVs1D6*zn7E2mHY4_o9i")
            .remoteDirectory("/apps/aic/shared/test/custom")
            .timeout(15000)
            .gzipEnabled(false)
            .build();

        try (SftpClient customClient = new SftpClient(customConsumerConfig, customProducerConfig)) {
            String fileName = "custom-test-" + System.currentTimeMillis() + ".txt";
            String testMessage = "Custom config test - " + System.currentTimeMillis();

            assertDoesNotThrow(() -> {
                customClient.sendMessage(fileName, testMessage);
            });

            logger.info("Successfully tested custom configuration with file: {}", fileName);
        }
    }

    @Test
    void testErrorHandling() {
        // Test with invalid configuration to verify error handling
        SftpClientConfig invalidConfig = new SftpClientConfig.Builder()
            .host("invalid.host.example.com")
            .port(22)
            .username("invalid_user")
            .password("invalid_password")
            .remoteDirectory("/invalid/path")
            .timeout(5000) // Short timeout for faster test
            .build();

        try (SftpClient invalidClient = new SftpClient(invalidConfig, invalidConfig)) {
            // This should throw an exception due to invalid configuration
            assertThrows(Exception.class, () -> {
                invalidClient.sendMessage("test.txt", "This should fail");
            });

            logger.info("Correctly handled invalid configuration");
        }
    }

    @Test
    void testFileExistenceCheck() throws Exception {
        // Test file existence checking before upload
        String fileName = TEST_FILE_PREFIX + "exists-" + System.currentTimeMillis() + ".txt";
        String testMessage = TEST_CONTENT + "for existence test";

        // Send file first time - should succeed
        assertDoesNotThrow(() -> {
            sftpClient.sendMessage(fileName, testMessage);
        });

        // Try to send same file again - should fail due to existence check
        assertThrows(RuntimeException.class, () -> {
            sftpClient.sendMessage(fileName, testMessage + " - second attempt");
        });

        logger.info("Successfully tested file existence checking for: {}", fileName);
    }

    @Test
    void testLargeFileHandling() throws Exception {
        // Test handling of larger files
        String fileName = TEST_FILE_PREFIX + "large-" + System.currentTimeMillis() + ".txt";
        StringBuilder largeContent = new StringBuilder();

        // Create a larger test content (about 10KB)
        for (int i = 0; i < 1000; i++) {
            largeContent.append("This is line ").append(i).append(" of the large test file content.\n");
        }

        String testMessage = largeContent.toString();

        assertDoesNotThrow(() -> {
            sftpClient.sendMessage(fileName, testMessage);
        });

        Thread.sleep(2000);

        // Try to receive the large file
        SftpClient.SftpMessage receivedMessage = sftpClient.receiveMessage();

        if (receivedMessage != null && receivedMessage.getFileName().equals(fileName)) {
            assertTrue(receivedMessage.getFileSize() > 5000, "Large file should be over 5KB");
            assertTrue(receivedMessage.getContent().contains("This is line"), "Content should match");
            logger.info("Successfully handled large file: {} ({} bytes)",
                       fileName, receivedMessage.getFileSize());
        } else {
            logger.info("Large file test completed - file may have been processed by another test");
        }
    }

    @Test
    void testSpecialCharacterHandling() throws Exception {
        // Test handling of files with special characters in content
        String fileName = TEST_FILE_PREFIX + "special-" + System.currentTimeMillis() + ".txt";
        String specialContent = "Special chars: àáâãäåæçèéêëìíîïðñòóôõö÷øùúûüýþÿ\n" +
                               "Symbols: !@#$%^&*()_+-=[]{}|;':\",./<>?\n" +
                               "Unicode: 你好世界 🌍 🚀 ⭐\n" +
                               "Newlines and tabs:\tTabbed\tContent\n";

        assertDoesNotThrow(() -> {
            sftpClient.sendMessage(fileName, specialContent);
        });

        Thread.sleep(2000);

        SftpClient.SftpMessage receivedMessage = sftpClient.receiveMessage();

        if (receivedMessage != null && receivedMessage.getFileName().equals(fileName)) {
            assertTrue(receivedMessage.getContent().contains("Special chars"),
                      "Should preserve special characters");
            assertTrue(receivedMessage.getContent().contains("你好世界"),
                      "Should preserve Unicode characters");
            logger.info("Successfully handled special characters in file: {}", fileName);
        } else {
            logger.info("Special character test completed - file may have been processed");
        }
    }

    @Test
    void testConcurrentOperations() throws Exception {
        // Test concurrent file operations
        CountDownLatch latch = new CountDownLatch(3);
        AtomicInteger successCount = new AtomicInteger(0);

        // Start multiple threads sending files concurrently
        for (int i = 1; i <= 3; i++) {
            final int threadNum = i;
            new Thread(() -> {
                try {
                    String fileName = TEST_FILE_PREFIX + "concurrent-" + threadNum + "-" + System.currentTimeMillis() + ".txt";
                    String content = "Concurrent test content from thread " + threadNum;

                    sftpClient.sendMessage(fileName, content);
                    successCount.incrementAndGet();
                    logger.info("Thread {} successfully sent file: {}", threadNum, fileName);
                } catch (Exception e) {
                    logger.error("Thread {} failed to send file: {}", threadNum, e.getMessage());
                } finally {
                    latch.countDown();
                }
            }).start();
        }

        // Wait for all threads to complete
        boolean completed = latch.await(30, TimeUnit.SECONDS);

        assertTrue(completed, "All concurrent operations should complete within timeout");
        assertTrue(successCount.get() >= 1, "At least one concurrent operation should succeed");

        logger.info("Successfully tested concurrent operations - {} out of 3 succeeded", successCount.get());
    }

    /**
     * Manual test method for interactive testing - not run automatically
     * Run this method manually to test against actual SFTP server
     */
    public static void main(String[] args) {
        Logger mainLogger = LoggerFactory.getLogger("SftpClientManualTest");

        try {
            mainLogger.info("Starting manual SFTP client test...");

            SftpClientConfig consumerConfig = SftpClientConfig.createConsumerConfig();
            SftpClientConfig producerConfig = SftpClientConfig.createProducerConfig();

            try (SftpClient client = new SftpClient(consumerConfig, producerConfig)) {

                // Test 1: Send a file
                mainLogger.info("Test 1: Sending file...");
                String fileName = "manual-test-" + System.currentTimeMillis() + ".txt";
                String testMessage = "Manual test message - " + System.currentTimeMillis();
                client.sendMessage(fileName, testMessage);
                mainLogger.info("File sent successfully: {}", fileName);

                // Test 2: Receive a file
                mainLogger.info("Test 2: Receiving file...");
                SftpClient.SftpMessage received = client.receiveMessage();
                if (received != null) {
                    mainLogger.info("Received file: {} with content: {}",
                                   received.getFileName(), received.getContent());
                } else {
                    mainLogger.info("No file received");
                }

                // Test 3: Start listener for a short time
                mainLogger.info("Test 3: Starting listener for 10 seconds...");
                client.startListener(message -> {
                    mainLogger.info("Listener received file: {} (size: {} bytes)",
                                   message.getFileName(), message.getFileSize());
                });

                Thread.sleep(10000);
                client.stopListener();
                mainLogger.info("Listener stopped");

            }

            mainLogger.info("Manual test completed successfully");

        } catch (Exception e) {
            mainLogger.error("Manual test failed: {}", e.getMessage(), e);
        }
    }
}
