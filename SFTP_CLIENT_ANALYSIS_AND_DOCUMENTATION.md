# SFTP Client Development and Configuration Analysis

## Overview

This document provides a comprehensive analysis of the SFTP configuration properties in the HIP services framework and documents the development of the SftpClient utility class for testing SFTP adapter and handler functionality.

## 1. Configuration Property Analysis

### DynamicSFTPAdapterConfig Analysis

#### Used Properties (with JSON bindings added):
- ✅ `host` - `@JsonProperty("sftp.consumer.host")` - Used in `SftpUtil.buildClientSession()`
- ✅ `port` - `@JsonProperty("sftp.consumer.port")` - Used in `SftpUtil.buildClientSession()`
- ✅ `username` - `@JsonProperty("sftp.consumer.username")` - Used in `SftpUtil.buildClientSession()`
- ✅ `password` - `@JsonProperty("sftp.consumer.password")` - Used in `SftpUtil.buildClientSession()`
- ✅ `privateKey` - `@JsonProperty("sftp.consumer.private.key")` - Used in `SftpUtil.buildClientSession()`
- ✅ `privateKeyPassphrase` - `@JsonProperty("sftp.consumer.private.key.passphrase")` - Used in `SftpUtil.buildClientSession()`
- ✅ `remoteDirectory` - `@JsonProperty("sftp.consumer.remote.directory")` - Used in `pollSftpForFiles()`
- ✅ `fileNamePattern` - `@JsonProperty("sftp.consumer.file.filter")` - Used in `pollSftpForFiles()`
- ✅ `pollingIntervalMs` - `@JsonProperty("sftp.consumer.polling.interval.ms")` - Used in `pollLoop()`
- ✅ `compressed` - `@JsonProperty("sftp.consumer.compressed")` - Used in `pollSftpForFiles()`
- ✅ `charset` - `@JsonProperty("sftp.consumer.charset")` - Used in `pollSftpForFiles()`
- ✅ `headersToExtract` - `@JsonProperty("sftp.consumer.headers.to.extract")` - Used in `buildMessage()`
- ✅ `postProcessAction` - `@JsonProperty("sftp.consumer.post.process.action")` - Used in `handlePostProcess()`
- ✅ `renamePattern` - `@JsonProperty("sftp.consumer.rename.pattern")` - Used in `handlePostProcess()`

#### Removed Unused Properties:
- ❌ `knownHostsFile` - Not used anywhere
- ❌ `deadLetterDirectory` - Not used anywhere
- ❌ `maxFileSizeBytes` - Not used anywhere
- ❌ `minFileAgeMs` - Not used anywhere
- ❌ `concurrency` - Not used anywhere
- ❌ `properties` - Not used anywhere (inherited from parent)
- ❌ `throttleSettings` - Not used anywhere
- ❌ `callbackUrl` - Not used anywhere
- ❌ `callbackToken` - Not used anywhere

### DynamicSftpHandlerConfig Analysis

#### Used Properties (with JSON bindings added):
- ✅ `host` - `@JsonProperty("sftp.producer.host")` - Used in `getOrCreateSftpClient()`
- ✅ `port` - `@JsonProperty("sftp.producer.port")` - Used in `getOrCreateSftpClient()`
- ✅ `username` - `@JsonProperty("sftp.producer.username")` - Used in `getOrCreateSftpClient()`
- ✅ `password` - `@JsonProperty("sftp.producer.password")` - Used in `getOrCreateSftpClient()`
- ✅ `privateKeyPath` - `@JsonProperty("sftp.producer.private.key.path")` - Used in `getOrCreateSftpClient()`
- ✅ `remoteDirectory` - `@JsonProperty("sftp.producer.remote.directory")` - Used in `handleMessage()`
- ✅ `timeout` - `@JsonProperty("sftp.producer.timeout")` - Used in `getOrCreateSftpClient()`
- ✅ `gzipEnabled` - `@JsonProperty("sftp.producer.gzip.enabled")` - Used in `handleMessage()`

#### Removed Unused Properties:
- ❌ `id` - Not used anywhere (should use inherited id from parent)
- ❌ `archiveEnabled` - Not used anywhere
- ❌ `parameters` - Not used anywhere

## 2. Configuration Changes Made

### DynamicSFTPAdapterConfig Updates:
1. Added missing `@JsonProperty` annotations for all used properties
2. Removed unused properties and their imports
3. Added Lombok `@Getter` and `@Setter` annotations
4. Cleaned up imports (removed unused ThrottleSettings and Map imports)

### DynamicSftpHandlerConfig Updates:
1. Added missing `@JsonProperty` annotations for all used properties
2. Removed unused properties and manual getters/setters
3. Added Lombok `@Getter` and `@Setter` annotations
4. Cleaned up imports and removed unused code

## 3. SftpClient Utility Development

### SftpClientConfig Class
Created a comprehensive configuration class that mirrors the patterns used in `KafkaClientConfig`:

**Key Features:**
- Supports both consumer (adapter testing) and producer (handler testing) configurations
- Builder pattern for easy configuration
- Pre-configured factory methods with test connection properties
- All properties from the cleaned-up adapter/handler configs

**Test Configuration Properties:**
```java
// Consumer Configuration
SftpClientConfig.createConsumerConfig()
    .host("DURNLDVAICBAT01.amer.dell.com")
    .port(22)
    .username("AMERICAS\\svc_npaicdvgsmrmqv1")
    .password("~UfIBrgxqj%2BVp%3FSTK69L178H")
    .remoteDirectory("/apps/aic/shared/test")

// Producer Configuration  
SftpClientConfig.createProducerConfig()
    .host("DURNLDVAICBAT01.amer.dell.com")
    .port(22)
    .username("AMERICAS\\svc_npaicdvgsmrmqv1")
    .password("~UfIBrgxqj%2BVp%3FSTK69L178H")
    .remoteDirectory("/apps/aic/shared/test")
```

### SftpClient Class
Created a comprehensive SFTP client utility that mirrors the implementation patterns from existing adapter/handler classes:

**Key Features:**
- **Message Sending**: Uses the same approach as `DynamicSftpOutputHandler`
- **Message Receiving**: Uses the same approach as `DynamicSFTPInputAdapter`
- **Connection Management**: Mirrors the SSH client and session management patterns
- **File Processing**: Supports compression, decompression, and post-processing actions
- **Continuous Monitoring**: Provides listener functionality for continuous file monitoring

**Core Methods:**
```java
// Send messages (handler testing)
sftpClient.sendMessage("test-file.txt", "Hello World");
sftpClient.sendMessage("test-file.bin", byteArray);

// Receive messages (adapter testing)
SftpMessage message = sftpClient.receiveMessage();
SftpMessage message = sftpClient.receiveMessage("*.xml");

// Continuous monitoring
sftpClient.startListener(message -> {
    System.out.println("Received: " + message.getFileName());
});

// Wait for specific number of files
List<SftpMessage> messages = sftpClient.waitForMessages(5, 30000, 
    message -> System.out.println("Got: " + message.getFileName()));
```

## 4. Usage Instructions

### Basic Usage Example:
```java
// Create configurations
SftpClientConfig consumerConfig = SftpClientConfig.createConsumerConfig();
SftpClientConfig producerConfig = SftpClientConfig.createProducerConfig();

// Create client
try (SftpClient sftpClient = new SftpClient(consumerConfig, producerConfig)) {
    
    // Test handler functionality (sending)
    sftpClient.sendMessage("test-message.txt", "Hello from HIP SFTP Client!");
    
    // Test adapter functionality (receiving)
    SftpMessage receivedMessage = sftpClient.receiveMessage();
    if (receivedMessage != null) {
        System.out.println("Received file: " + receivedMessage.getFileName());
        System.out.println("Content: " + receivedMessage.getContent());
    }
    
    // Continuous monitoring
    sftpClient.startListener(message -> {
        System.out.println("File received: " + message.getFileName());
        System.out.println("Size: " + message.getFileSize() + " bytes");
    });
    
    Thread.sleep(60000); // Monitor for 1 minute
    sftpClient.stopListener();
}
```

### Custom Configuration Example:
```java
SftpClientConfig customConfig = new SftpClientConfig.Builder()
    .host("custom-sftp-server.com")
    .port(2222)
    .username("testuser")
    .password("testpass")
    .remoteDirectory("/custom/path")
    .fileNamePattern("*.json")
    .pollingIntervalMs(30000L)
    .postProcessAction("rename")
    .renamePattern("{file}.processed")
    .build();
```

## 5. Technical Implementation Details

### Connection Management:
- Uses Apache SSHD client library (same as existing implementations)
- Supports both password and key-based authentication
- Implements connection reuse and session management
- Includes proper resource cleanup and error handling

### File Operations:
- Mirrors exact file reading/writing patterns from existing adapters/handlers
- Supports compression/decompression using `CompressionUtil`
- Implements file existence checking before upload
- Supports post-processing actions (delete/rename)

### Error Handling:
- Comprehensive exception handling and logging
- Graceful degradation for connection issues
- Proper resource cleanup in all scenarios

## 6. Testing Recommendations

1. **Unit Tests**: Create unit tests for individual methods
2. **Integration Tests**: Test against actual SFTP server
3. **Performance Tests**: Validate file transfer performance
4. **Error Scenario Tests**: Test connection failures, authentication issues, etc.
5. **Compatibility Tests**: Ensure compatibility with existing adapter/handler implementations

## 7. Future Enhancements

1. **Metrics Integration**: Add metrics collection for monitoring
2. **Retry Logic**: Implement configurable retry mechanisms
3. **Batch Operations**: Support for batch file operations
4. **Advanced Filtering**: More sophisticated file filtering options
5. **Security Enhancements**: Additional security features and validations
