package com.dell.it.hip.client;

import java.util.List;

/**
 * Configuration class for SftpClient that mirrors the property structure
 * used in DynamicSFTPAdapterConfig and DynamicSftpHandlerConfig.
 * 
 * This class can be used for both consumer (adapter testing) and producer (handler testing)
 * configurations by using the appropriate property prefixes.
 */
public class SftpClientConfig {
    
    // Core SFTP connection properties
    private String host;
    private Integer port = 22;
    private String username;
    private String password;
    
    // Authentication
    private String privateKey;
    private String privateKeyPassphrase;
    private String privateKeyPath;
    
    // Directory and file handling
    private String remoteDirectory;
    private String fileNamePattern;
    
    // Connection settings
    private Integer timeout = 10000; // Default 10 seconds
    
    // Consumer-specific properties (adapter testing)
    private Long pollingIntervalMs = 60000L;
    private boolean compressed = false;
    private String charset = "UTF-8";
    private List<String> headersToExtract;
    private String postProcessAction; // delete/rename
    private String renamePattern;
    
    // Producer-specific properties (handler testing)
    private Boolean gzipEnabled = false;
    
    // Default constructor
    public SftpClientConfig() {}
    
    // Builder pattern constructor
    private SftpClientConfig(Builder builder) {
        this.host = builder.host;
        this.port = builder.port;
        this.username = builder.username;
        this.password = builder.password;
        this.privateKey = builder.privateKey;
        this.privateKeyPassphrase = builder.privateKeyPassphrase;
        this.privateKeyPath = builder.privateKeyPath;
        this.remoteDirectory = builder.remoteDirectory;
        this.fileNamePattern = builder.fileNamePattern;
        this.timeout = builder.timeout;
        this.pollingIntervalMs = builder.pollingIntervalMs;
        this.compressed = builder.compressed;
        this.charset = builder.charset;
        this.headersToExtract = builder.headersToExtract;
        this.postProcessAction = builder.postProcessAction;
        this.renamePattern = builder.renamePattern;
        this.gzipEnabled = builder.gzipEnabled;
    }
    
    /**
     * Create a consumer configuration with the provided test properties
     */
    public static SftpClientConfig createConsumerConfig() {
        return new Builder()
            .host("DURNLDVAICBAT01.amer.dell.com")
            .port(22)
            .username("AMERICAS\\svc_npaicdvgsmrmqv1")
            .password("~UfIBrgxqj%2BVp%3FSTK69L178H")
            .remoteDirectory("/apps/aic/shared/test")
            .fileNamePattern("*")
            .pollingIntervalMs(60000L)
            .compressed(false)
            .charset("UTF-8")
            .postProcessAction("delete")
            .build();
    }
    
    /**
     * Create a producer configuration with the provided test properties
     */
    public static SftpClientConfig createProducerConfig() {
        return new Builder()
            .host("DURNLDVAICBAT01.amer.dell.com")
            .port(22)
            .username("AMERICAS\\svc_npaicdvgsmrmqv1")
            .password("~UfIBrgxqj%2BVp%3FSTK69L178H")
            .remoteDirectory("/apps/aic/shared/test")
            .timeout(10000)
            .gzipEnabled(false)
            .build();
    }
    
    // Getters and setters
    public String getHost() { return host; }
    public void setHost(String host) { this.host = host; }
    
    public Integer getPort() { return port; }
    public void setPort(Integer port) { this.port = port; }
    
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }
    
    public String getPrivateKey() { return privateKey; }
    public void setPrivateKey(String privateKey) { this.privateKey = privateKey; }
    
    public String getPrivateKeyPassphrase() { return privateKeyPassphrase; }
    public void setPrivateKeyPassphrase(String privateKeyPassphrase) { this.privateKeyPassphrase = privateKeyPassphrase; }
    
    public String getPrivateKeyPath() { return privateKeyPath; }
    public void setPrivateKeyPath(String privateKeyPath) { this.privateKeyPath = privateKeyPath; }
    
    public String getRemoteDirectory() { return remoteDirectory; }
    public void setRemoteDirectory(String remoteDirectory) { this.remoteDirectory = remoteDirectory; }
    
    public String getFileNamePattern() { return fileNamePattern; }
    public void setFileNamePattern(String fileNamePattern) { this.fileNamePattern = fileNamePattern; }
    
    public Integer getTimeout() { return timeout; }
    public void setTimeout(Integer timeout) { this.timeout = timeout; }
    
    public Long getPollingIntervalMs() { return pollingIntervalMs; }
    public void setPollingIntervalMs(Long pollingIntervalMs) { this.pollingIntervalMs = pollingIntervalMs; }
    
    public boolean isCompressed() { return compressed; }
    public void setCompressed(boolean compressed) { this.compressed = compressed; }
    
    public String getCharset() { return charset; }
    public void setCharset(String charset) { this.charset = charset; }
    
    public List<String> getHeadersToExtract() { return headersToExtract; }
    public void setHeadersToExtract(List<String> headersToExtract) { this.headersToExtract = headersToExtract; }
    
    public String getPostProcessAction() { return postProcessAction; }
    public void setPostProcessAction(String postProcessAction) { this.postProcessAction = postProcessAction; }
    
    public String getRenamePattern() { return renamePattern; }
    public void setRenamePattern(String renamePattern) { this.renamePattern = renamePattern; }
    
    public Boolean getGzipEnabled() { return gzipEnabled; }
    public void setGzipEnabled(Boolean gzipEnabled) { this.gzipEnabled = gzipEnabled; }

    // Builder pattern for easy configuration
    public static class Builder {
        private String host;
        private Integer port = 22;
        private String username;
        private String password;
        private String privateKey;
        private String privateKeyPassphrase;
        private String privateKeyPath;
        private String remoteDirectory;
        private String fileNamePattern;
        private Integer timeout = 10000;
        private Long pollingIntervalMs = 60000L;
        private boolean compressed = false;
        private String charset = "UTF-8";
        private List<String> headersToExtract;
        private String postProcessAction;
        private String renamePattern;
        private Boolean gzipEnabled = false;

        public Builder host(String host) { this.host = host; return this; }
        public Builder port(Integer port) { this.port = port; return this; }
        public Builder username(String username) { this.username = username; return this; }
        public Builder password(String password) { this.password = password; return this; }
        public Builder privateKey(String privateKey) { this.privateKey = privateKey; return this; }
        public Builder privateKeyPassphrase(String privateKeyPassphrase) { this.privateKeyPassphrase = privateKeyPassphrase; return this; }
        public Builder privateKeyPath(String privateKeyPath) { this.privateKeyPath = privateKeyPath; return this; }
        public Builder remoteDirectory(String remoteDirectory) { this.remoteDirectory = remoteDirectory; return this; }
        public Builder fileNamePattern(String fileNamePattern) { this.fileNamePattern = fileNamePattern; return this; }
        public Builder timeout(Integer timeout) { this.timeout = timeout; return this; }
        public Builder pollingIntervalMs(Long pollingIntervalMs) { this.pollingIntervalMs = pollingIntervalMs; return this; }
        public Builder compressed(boolean compressed) { this.compressed = compressed; return this; }
        public Builder charset(String charset) { this.charset = charset; return this; }
        public Builder headersToExtract(List<String> headersToExtract) { this.headersToExtract = headersToExtract; return this; }
        public Builder postProcessAction(String postProcessAction) { this.postProcessAction = postProcessAction; return this; }
        public Builder renamePattern(String renamePattern) { this.renamePattern = renamePattern; return this; }
        public Builder gzipEnabled(Boolean gzipEnabled) { this.gzipEnabled = gzipEnabled; return this; }

        public SftpClientConfig build() {
            return new SftpClientConfig(this);
        }
    }
}
