package com.dell.it.hip.config.adapters;

import java.util.List;
import java.util.Map;

import com.dell.it.hip.util.ThrottleSettings;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;
@Getter
@Setter
public class DynamicSFTPAdapterConfig extends AdapterConfig {
   
	@JsonProperty("sftp.consumer.host")
	private String host;
	
	@JsonProperty("sftp.consumer.port")
    private Integer port;
	
	@JsonProperty("sftp.consumer.username")
    private String username;
	
	@JsonProperty("sftp.consumer.password")
    private String password;
    private String privateKey;
    private String privateKeyPassphrase;
    private String knownHostsFile;
    
	@JsonProperty("sftp.consumer.remote.directory")
    private String remoteDirectory;
	
	@JsonProperty("sftp.consumer.file.filter")
    private String fileNamePattern;
    private Long pollingIntervalMs = 60000L;
    private boolean compressed = false;
    private String charset = "UTF-8";
    private List<String> headersToExtract;
    private String postProcessAction; // delete/rename
    private String renamePattern;
    private String deadLetterDirectory;
    private Integer maxFileSizeBytes;
    private Integer minFileAgeMs;
    private Integer concurrency = 1;
    private Map<String, ?> properties;
    private ThrottleSettings throttleSettings;

    // === Callback additions ===
    private String callbackUrl;
    private String callbackToken; // optional for securing callback

    // Override parent's properties method to provide specific type
    @Override
    public void setProperties(Map<String, ?> properties) {
        super.setProperties(properties);
    }

    // Convenience method for String-specific properties
    public void setStringProperties(Map<String, String> properties) {
        super.setProperties(properties);
    }

    // Getters & setters
}